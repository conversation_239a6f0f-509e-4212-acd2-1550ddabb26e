import { FirebaseService } from '@/api/firebase/firebase.service'
import { Audio } from "@/entity/Audio"
import { AudioAuthor } from "@/entity/AudioAuthor"
import { AudioFile } from '@/entity/AudioFile'
import { AudioHandler } from "@/entity/AudioHandler"
import { AudioStatus } from "@/entity/AudioStatus"
import { AudioTag } from "@/entity/AudioTag"
import { AudioType } from "@/entity/AudioType"
import { Content } from '@/entity/Content'
import { ContentCategory } from '@/entity/ContentCategory'
import { Notification, NotificationType } from '@/entity/Notification'
import { PlaylistItem } from '@/entity/PlaylistItem'
import { VideoStatus } from "@/entity/VideoStatus"
import { HttpService } from '@nestjs/axios'
import { Injectable } from '@nestjs/common'
import Bottleneck from 'bottleneck'
import { readFileSync } from 'fs'
import * as moment from 'moment'
import { FileSystemStoredFile } from "nestjs-form-data"
import slugify from 'slugify'
import { <PERSON>ike, <PERSON>Null, Not } from 'typeorm'

@Injectable()
export class AudioService {
    private statuses = [];
    private videoStatuses = [];
    private authors = [];
    private tags = []
    private entities = {}

    constructor(
        private readonly firebaseService: FirebaseService,
        private readonly httpService: HttpService
    ) {}

    async getAll(filters: any) {
        const where = {}
        const sort = filters.sort;
        const page: number = filters.page || 1

        if(filters.title) {
            where['title'] = ILike(`%${filters.title}%`)
        }
        if(filters.author) {
            where['author'] = filters.author
        }

        const params = {
            where,
            // take: 10,
            // skip: (page-1) * 10,
            relations: ['likes', 'favourites', 'listened'],
            order: {}
        }
        if(sort == 'views') {
            params.order = {
                views: 'DESC'
            }
        }
        if(sort == 'likes') {
            params.order = {
                likes: {
                    id: 'ASC'
                }
            }
        }
        if(sort == 'favourites') {
            params['order'] = {
                favourites: {
                    id: 'ASC'
                }
            }
        }

        const total = await Audio.count(params)
        let items = await Audio.find(params);

        items = items.map((e: any) => {
            return {
                ...e,
                listened: e.listened.reduce((p, c) => p + c.count, 0)
            }
        })

        return {total, items}
    }

    async importFile({path}: FileSystemStoredFile) {
        const file = JSON.parse(readFileSync(path, 'utf8'));
        return await this.import(file)
    }

    async import(file: any) {
        this.statuses = file.audioStatuses
        this.authors = file.sannyasis
        this.videoStatuses = file.videoStatuses
        this.entities = {}
        const limiter = new Bottleneck({
            maxConcurrent: 10
        })
        // const entities = {
        //     'sannyasis': AudioAuthor,
        //     'audioStatuses': AudioStatus,
        //     'videoStatuses': VideoStatus,
        //     'types': AudioType,
        //     'tags': AudioTag,
        //     'handlers': AudioHandler
        // }
        const promises = []
        const entities = [
            {
                field: 'audioStatus',
                list: 'audioStatuses',
                entity: AudioStatus
            },
            {
                field: 'author',
                list: 'sannyasis',
                entity: AudioAuthor
            },
            {
                field: 'reader',
                list: 'sannyasis',
                entity: AudioAuthor
            },
            {
                field: 'videoStatus',
                list: 'videoStatuses',
                entity: VideoStatus
            },
            {
                field: 'type',
                list: 'types',
                entity: AudioType
            },
            {
                field: 'handler',
                list: 'handlers',
                entity: AudioHandler
            },
        ]
        for(let entity of entities) {
            if(!(entity.list in file)) continue;
            for(let key in file[entity.list]) {
                promises.push(
                    limiter.schedule(
                        () => this.createEntity(entity.field, entity.entity, file[entity.list][key])
                    )
                )
            }
        }

        await Promise.all(promises)

        const audio: any = Object.values(file.lectures)

        for(let item of audio) {
            if(!item.advayta) continue
            limiter.schedule(() => this.create(item)).then((audio: any) => {
                if(audio && item.tags) this.addTags(audio, Object.keys(item.tags))
            })
        }

        return file
    }

    private mapLists(audio) {
        for(let key in audio) {
            if(!(key in this.entities)) continue;
            if(key === 'tags')  {
                audio[key] = Object.keys(audio[key])
            } else {
                audio[key] = this.entities[key].find(e => e.key === audio[key]) || '';
            }
        }
        return audio
    }

    private async create(audio) {
        audio = this.mapLists(audio);
        const validation: any = this.checkLectureValidation(audio)
        if(validation.length) return false;
        const params = {
            external_id: audio.key,
            status: audio.audioStatus?.value,
            title: audio.title,
            link: audio.advayta,
            author: audio.author?.value,
            reader: audio.reader?.value,
            comment: audio.comment,
            date: moment(audio.date).format('YYYY-MM-DD'),
            description: audio.description,
            duration: this.getDuration(audio.duration),
            youtube: audio.youtube ?? null,
            videoStatus: audio.videoStatus?.value,
            seo_title: audio.seo_title,
            seo_description: audio.seo_description,
            text: audio.text,
            text_link: audio.text_link,
        }
        const item = await Audio.findOne({
            where: {external_id: audio.key},
            relations: {
                tags: true
            }
        })
        if(item) {
            Object.assign(item, params);
            return await item.save();
        }
        const {id} = await Audio.save(params);
        return await Audio.findOne({
            where: {id},
            relations: {
                tags: true
            }
        })
    }

    private async addTags(audio: Audio, tags: any[]) {
        let audioTags = await AudioTag.find()
        let tmpTags = audioTags.filter(e => tags.map(k => k.key).includes(e.external_id))
        audio.tags = tmpTags
        return await audio.save()
    }

    private async createEntity(key, entity, item) {
        this.entities[key] = [...(this.entities[key] || []), item]
        const update = await entity.findOneBy({external_id: item.key})
        if(update) return
        await entity.save({
            external_id: item.key,
            name: item.value
        })
    }
    private getDuration(duration: string) {
        if(!duration) return null
        const arr = duration.split(':')
        return (+arr[0]) * 60 * 60 + (+arr[1]) * 60 + (+arr[2]);
    }
    public async getAuthors() {
        return await AudioAuthor.find();
    }

    public async importOne(audio: any) {
        //const validation: any = this.checkLectureValidation(audio)
        //if(validation.length) return validation;
        this.tags = await AudioTag.find()

        if (audio.lecture_link && audio.lecture_link.includes('supabase.co')) {
            try {
                const textContent = await this.downloadTextFromUrl(audio.lecture_link);
                if (textContent) {
                    const createdContent = await this.createContentFromLecture(audio, textContent);
                    if (createdContent) {

                        audio.lecture_link = this.generateContentUrl(createdContent);
                    }
                }
            } catch (error) {
                console.error('Ошибка при обработке lecture_link:', error);
            }
        }

        if(audio.text) {
            audio.text = this.firebaseService.copyFileFromTemp('text', audio, 'lections/text')[0]
        }

        if(audio.preview) {
            audio.preview = this.firebaseService.copyFileFromTemp('preview', audio, 'lections/preview')[0]
        }

        if(audio.advayta) {
            audio.advayta = this.firebaseService.copyFileFromTemp('advayta', audio, 'lections/audio')[0]
        }

        const params = {
            external_id: audio.key,
            status: audio.audioStatus?.value || '',
            title: audio.title,
            link: audio.advayta,
            author: audio.author?.value || '',
            reader: audio.reader?.value || '',
            comment: audio.comment,
            date: moment(audio.date).format('YYYY-MM-DD'),
            description: audio.description,
            duration: this.getDuration(audio.duration),
            youtube: audio.youtube || '',
            videoStatus: audio.videoStatus?.value || '',
            seo_title: audio.seo_title,
            seo_description: audio.seo_description,
            text: audio.text,
            text_link: audio.text_link,
            lecture_link: audio.lecture_link,
            preview: audio.preview,
            paid: audio.paid
        }

        let item = await Audio.findOne({
            where: {external_id: audio.key},
            relations: {
                tags: true
            }
        })
        if(item) {
            Object.assign(item, params);
            await this.addTags(item, audio.tags)
            return await item.save();
        }
        const result = await Audio.save(params);
        item = await Audio.findOne({
            where: {external_id: audio.key},
            relations: {
                tags: true
            }
        })
        await this.addTags(item, audio.tags)

        await Notification.save({
            type: NotificationType.CONTENT_AUDIO_LECTURE_PUBLISHED,
            title: audio.title,
            link: `/ru/audiogallery/audiolektsii/${audio.key}`
        })

        return result
    }

    async getTags() {
        return await AudioTag.find()
    }

    checkLectureValidation(audio: any) {
        const errors = []
        if(!audio) errors.push('Передан пустой объект')
        if(!audio.title) errors.push('Название не заполнено')
        if(!audio.type || !['Лекция', 'Ответы на вопросы'].includes(audio.type.value)) {
            errors.push('Тип Лекция, Ответы на вопросы')
        }
        if(!audio.audioStatus || audio.audioStatus.value !== 'Обработан') {
            errors.push('Статус аудио должен быть "Обработан"')
        }
        if(!audio.advayta) {
            errors.push('Не заполнена ссылка на mp3-файл')
        }
        return errors
    }

    async getPlaylists() {
        return await PlaylistItem.find({
            where: {
                audioFile: Not(IsNull())
            }
        })
    }

    async getLecturesAndAudio(term: string) {
      const lecturesPromise = Audio.find({
        select: ['id', 'title'],
        where: {
          title: ILike(`%${term}%`)
        },
        take: 5
      })

      const audioFilesPromise = AudioFile.find({
        select: ['id', 'title'],
        where: {
          title: ILike(`%${term}%`)
        },
        take: 5
      })

      let [lectures, audioFiles] = await Promise.all([
        lecturesPromise,
        audioFilesPromise,
      ]);

      const formattedLectures = lectures.map((e) => ({...e, type: 'audio'}))
      const formattedAudioFiles = audioFiles.map((e) => ({...e, type: 'audioFile'}))

      return { items: [...formattedLectures, ...formattedAudioFiles] }
    }

    private async downloadTextFromUrl(url: string): Promise<string | null> {
        try {
            const { data } = await this.httpService.axiosRef.get(url);
            return data;
        } catch (error) {
            console.error('Ошибка при скачивании текста по URL:', error);
            return null;
        }
    }

    private convertTextToHtml(text: string): string {
        if (!text) return '';

        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;')
            .split('\n\n')
            .map(paragraph => {
                if (paragraph.trim()) {
                    const formattedParagraph = paragraph
                        .trim()
                        .replace(/\n/g, '<br>');
                    return `<p>${formattedParagraph}</p>`;
                }
                return '';
            })
            .filter(p => p)
            .join('\n');
    }

    private async createContentFromLecture(audio: any, textContent: string) {
        try {
            let lecturesCategory = await ContentCategory.findOne({
                where: { title: 'Лекции' }
            });

            if (!lecturesCategory) {
                lecturesCategory = await ContentCategory.save({
                    title: 'Лекции',
                    slug: slugify('Лекции', { lower: true }),
                    active: true,
                    order: 1
                });
            }

            const contentSlug = slugify(audio.title || `lecture-${audio.key}`, { lower: true });

            const existingContent = await Content.findOne({
                where: { slug: contentSlug },
                relations: ['category']
            });

            if (existingContent) {
                return existingContent;
            }

            const htmlContent = this.convertTextToHtml(textContent);

            const newContent = await Content.save({
                title: audio.title || `Лекция ${audio.key}`,
                slug: contentSlug,
                seo_title: audio.seo_title || audio.title || `Лекция ${audio.key}`,
                seo_description: audio.seo_description || audio.description || '',
                content: htmlContent,
                category: lecturesCategory,
                author: audio.author?.value || audio.author || '',
                active: true,
                paid: audio.paid || false,
                lang: 'ru'
            });

            const contentWithCategory = await Content.findOne({
                where: { id: newContent.id },
                relations: ['category']
            });

            return contentWithCategory;

        } catch (error) {
            console.error('Ошибка при создании контента из лекции:', error);
            return null;
        }
    }

    private generateContentUrl(content: Content): string {
        const baseUrl = process.env.CLIENT_URL || 'https://advayta.org';
        return `${baseUrl}/ru/categories/${content.category.id}/${content.slug}`;
    }
}
