import { AuthService } from "@/services/auth.service";
import { CanActivateFn } from '@angular/router';
import { inject } from "@angular/core";
import { Router } from "@angular/router";

export const authGuard: CanActivateFn = async (route, state) => {
  const authService = inject(AuthService)
  const router = inject(Router)
  const isLoginRoute = ['/signin', '/signup'].includes(state.url)

  if(authService.isAuth && isLoginRoute) {
    await router.navigate(['/'])
    return false
  }

  if (!authService.isAuth && !isLoginRoute) {
    // Сохраняем текущий URL для перенаправления после входа
    await router.navigate(['/ru/signin'], { queryParams: { return: state.url } });
    return false;
  }

  return true;
};
