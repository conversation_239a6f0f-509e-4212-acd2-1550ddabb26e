export type ThemeType = 'light' | 'dark';
export type FontType = 'prata' | 'open-sans' | 'montserrat';
export type TextWidthType = 'narrow' | 'medium' | 'full';

export interface UserSettings {
  theme: ThemeType;
  font: FontType;
  fontSize: number;
  textWidth: TextWidthType;
}

export const DEFAULT_USER_SETTINGS: UserSettings = {
  theme: 'light',
  font: 'prata',
  fontSize: 18,
  textWidth: 'narrow'
};
