import { Injectable, inject, PLATFORM_ID, signal } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { ProfileService } from './profile.service';
import { UserSettings, DEFAULT_USER_SETTINGS, ThemeType, FontType, TextWidthType } from '@/interfaces/user-settings';

@Injectable({
  providedIn: 'root'
})
export class UserSettingsService {
  private platformId = inject(PLATFORM_ID);
  private profileService = inject(ProfileService);
  
  // Signals for reactive state management
  theme = signal<ThemeType>(DEFAULT_USER_SETTINGS.theme);
  font = signal<FontType>(DEFAULT_USER_SETTINGS.font);
  fontSize = signal<number>(DEFAULT_USER_SETTINGS.fontSize);
  textWidth = signal<TextWidthType>(DEFAULT_USER_SETTINGS.textWidth);

  private readonly STORAGE_PREFIX = 'userSettings_';

  constructor() {
    // Load settings when service is initialized
    this.loadSettings();
  }

  /**
   * Get storage key for current user
   */
  private getStorageKey(): string {
    const userId = this.profileService.profile?.id;
    return userId ? `${this.STORAGE_PREFIX}${userId}` : `${this.STORAGE_PREFIX}guest`;
  }

  /**
   * Load settings from localStorage
   */
  loadSettings(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    try {
      const storageKey = this.getStorageKey();
      const stored = localStorage.getItem(storageKey);
      
      if (stored) {
        const settings: UserSettings = JSON.parse(stored);
        
        // Update signals with loaded settings
        this.theme.set(settings.theme || DEFAULT_USER_SETTINGS.theme);
        this.font.set(settings.font || DEFAULT_USER_SETTINGS.font);
        this.fontSize.set(settings.fontSize || DEFAULT_USER_SETTINGS.fontSize);
        this.textWidth.set(settings.textWidth || DEFAULT_USER_SETTINGS.textWidth);
        
        // Apply settings to DOM
        this.applySettings();
      } else {
        // Apply default settings
        this.applySettings();
      }
    } catch (error) {
      console.error('Error loading user settings:', error);
      // Fallback to default settings
      this.resetToDefaults();
    }
  }

  /**
   * Save current settings to localStorage
   */
  private saveSettings(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    try {
      const settings: UserSettings = {
        theme: this.theme(),
        font: this.font(),
        fontSize: this.fontSize(),
        textWidth: this.textWidth()
      };

      const storageKey = this.getStorageKey();
      localStorage.setItem(storageKey, JSON.stringify(settings));
    } catch (error) {
      console.error('Error saving user settings:', error);
    }
  }

  /**
   * Apply current settings to the DOM
   */
  private applySettings(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    const body = document.body;
    
    // Apply theme
    body.classList.remove('light-theme', 'dark-theme');
    body.classList.add(`${this.theme()}-theme`);
    
    // Apply font
    body.classList.remove('font-prata', 'font-open-sans', 'font-montserrat');
    body.classList.add(`font-${this.font()}`);
    
    // Apply font size
    body.style.setProperty('--user-font-size', `${this.fontSize()}px`);
    
    // Apply text width
    body.classList.remove('text-width-narrow', 'text-width-medium', 'text-width-full');
    body.classList.add(`text-width-${this.textWidth()}`);
  }

  /**
   * Update theme setting
   */
  setTheme(theme: ThemeType): void {
    this.theme.set(theme);
    this.applySettings();
    this.saveSettings();
  }

  /**
   * Update font setting
   */
  setFont(font: FontType): void {
    this.font.set(font);
    this.applySettings();
    this.saveSettings();
  }

  /**
   * Update font size setting
   */
  setFontSize(fontSize: number): void {
    // Ensure font size is within valid range
    const clampedSize = Math.max(12, Math.min(30, fontSize));
    this.fontSize.set(clampedSize);
    this.applySettings();
    this.saveSettings();
  }

  /**
   * Update text width setting
   */
  setTextWidth(textWidth: TextWidthType): void {
    this.textWidth.set(textWidth);
    this.applySettings();
    this.saveSettings();
  }

  /**
   * Reset all settings to defaults
   */
  resetToDefaults(): void {
    this.theme.set(DEFAULT_USER_SETTINGS.theme);
    this.font.set(DEFAULT_USER_SETTINGS.font);
    this.fontSize.set(DEFAULT_USER_SETTINGS.fontSize);
    this.textWidth.set(DEFAULT_USER_SETTINGS.textWidth);
    this.applySettings();
    this.saveSettings();
  }

  /**
   * Get current settings as object
   */
  getCurrentSettings(): UserSettings {
    return {
      theme: this.theme(),
      font: this.font(),
      fontSize: this.fontSize(),
      textWidth: this.textWidth()
    };
  }

  /**
   * Clear settings for current user (useful on logout)
   */
  clearSettings(): void {
    if (!isPlatformBrowser(this.platformId)) {
      return;
    }

    try {
      const storageKey = this.getStorageKey();
      localStorage.removeItem(storageKey);
      this.resetToDefaults();
    } catch (error) {
      console.error('Error clearing user settings:', error);
    }
  }

  /**
   * Reload settings (useful when user changes)
   */
  reloadSettings(): void {
    this.loadSettings();
  }
}
